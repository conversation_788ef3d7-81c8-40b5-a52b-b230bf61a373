/**
 * Base class for all node executors
 */
class BaseNodeExecutor {
  /**
   * Constructor for the base node executor
   * @param {Object} prisma - Prisma client instance
   * @param {Object} io - Socket.io instance
   */
  constructor(prisma, io) {
    this.prisma = prisma;
    this.io = io;
  }

  /**
   * Execute a node
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node to execute
   * @param {Object} node - The node data
   * @param {Object} inputs - The inputs to the node
   * @returns {Promise<{output: Object, success: boolean, error: Error|null}>} - The execution result
   */
  async execute(workflowRunId, nodeId, node, inputs) {
    throw new Error('Method not implemented. Each node executor must implement this method.');
  }

  /**
   * Process inputs from previous nodes
   * @param {Object} inputs - The inputs from previous nodes
   * @returns {string} - Formatted string of previous results
   */
  formatPreviousResults(inputs) {
    if (!inputs || Object.keys(inputs).length === 0 || inputs.isStartNode) {
      return '';
    }

    return Object.entries(inputs)
      .map(([sourceNodeId, output]) => {
        // Format the output - prefer result property if available
        const outputContent = output.result || JSON.stringify(output, null, 2);
        return `Input from node: ${sourceNodeId}: \n${outputContent}`;
      })
      .join('\n');
  }

  /**
   * Log node execution
   * @param {string} nodeId - The ID of the node
   * @param {string} nodeType - The type of the node
   * @param {Object} inputs - The inputs to the node
   * @param {Object} output - The output from the node
   */
  logExecution(nodeId, nodeType, inputs, output) {
    console.log(`[Node Execution] Node ${nodeId} (${nodeType}) - OUTPUTS:`, JSON.stringify(output, null, 2));
  }

  /**
   * Create execution log
   * @param {string} nodeId - The ID of the node
   * @param {string} nodeType - The type of the node
   * @param {Object} inputs - The inputs to the node
   * @param {Object} output - The output from the node
   * @param {boolean} success - Whether the execution was successful
   * @param {Error|null} error - Any error that occurred
   * @returns {Object} - The execution log
   */
  createExecutionLog(nodeId, nodeType, inputs, output, success, error) {
    return {
      nodeId,
      nodeType,
      executionTime: new Date().toISOString(),
      inputs,
      outputs: output,
      success,
      error: error ? error.message : null
    };
  }

  /**
   * Emit execution details via WebSockets
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node
   * @param {Object} executionLog - The execution log
   */
  emitExecutionDetails(workflowRunId, nodeId, executionLog) {
    this.io.emit('nodeExecution', {
      workflowRunId,
      nodeId,
      executionDetails: executionLog
    });
  }

  /**
   * Update node run status in the database
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node
   * @param {boolean} success - Whether the execution was successful
   * @param {Object} output - The output from the node
   * @param {Error|null} error - Any error that occurred
   */
  async updateNodeRunStatus(workflowRunId, nodeId, success, output, error) {
    await this.prisma.nodeRun.update({
      where: {
        workflowRunId_nodeId: {
          workflowRunId: workflowRunId,
          nodeId: nodeId
        }
      },
      data: {
        status: success ? 'SUCCESS' : 'FAILED',
        finishedAt: new Date(),
        output: error ? { ...output, error: error.message } : output
      }
    });
  }

  /**
   * Get all node results for the workflow run
   * @param {string} workflowRunId - The ID of the workflow run
   * @returns {Promise<Object>} - All node results
   */
  async getAllNodeResults(workflowRunId) {
    try {
      // Get all node runs for this workflow
      const allNodeRuns = await this.prisma.nodeRun.findMany({
        where: {
          workflowRunId: workflowRunId,
          status: 'SUCCESS' // Only include successful nodes
        }
      });

      // Create a map of node IDs to their outputs
      const nodeOutputs = {};
      for (const nodeRun of allNodeRuns) {
        if (nodeRun.output) {
          try {
            const output = typeof nodeRun.output === 'string'
              ? JSON.parse(nodeRun.output)
              : nodeRun.output;

            nodeOutputs[nodeRun.nodeId] = output;
          } catch (error) {
            console.error(`Error parsing output for node ${nodeRun.nodeId}:`, error);
          }
        }
      }

      return nodeOutputs;
    } catch (error) {
      console.error(`Error getting all node results:`, error);
      return {};
    }
  }

  /**
   * Replace variables in text with values from node outputs
   * @param {string} text - The text to process
   * @param {Array} selectedOutputs - The selected outputs
   * @param {Object} allNodeResults - All node results
   * @param {Object} directInputs - Direct inputs to the node
   * @returns {string} - The processed text
   */
  replaceVariables(text, selectedOutputs, allNodeResults, directInputs) {
    if (!text) return text;

    // Create a combined source of node outputs
    const nodeOutputs = { ...directInputs, ...allNodeResults };

    // Replace variables in the format {nodeId.key}
    return text.replace(/\{([^}]+)\}/g, (match, path) => {
      const [nodeId, key] = path.split('.');

      // Check if this variable is in the selected outputs
      const isSelected = selectedOutputs.some(
        selection => selection.nodeId === nodeId && selection.key === key
      );

      if (!isSelected) {
        return match; // Keep the original if not selected
      }

      // Get the node output
      const nodeOutput = nodeOutputs[nodeId];
      if (!nodeOutput) {
        return match; // Keep the original if node output not found
      }

      // Extract the value
      let value;
      if (key === 'result') {
        value = nodeOutput.result;
      } else if (key.includes('.')) {
        // Handle nested paths
        const parts = key.split('.');
        let current = nodeOutput;
        for (const part of parts) {
          if (current && typeof current === 'object' && part in current) {
            current = current[part];
          } else {
            return match; // Keep the original if path not found
          }
        }
        value = current;
      } else {
        value = nodeOutput[key];
      }

      // Return the value or the original if undefined
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Process selected inputs based on selectedOutputs configuration
   * @param {Object} inputs - Direct inputs to the node
   * @param {Array} selectedOutputs - Array of selected output configurations
   * @param {string} workflowRunId - The workflow run ID for fetching missing nodes
   * @returns {Promise<Object>} - Filtered inputs based on selection
   */
  async processSelectedInputs(inputs, selectedOutputs, workflowRunId) {
    console.log(`[BaseNodeExecutor] processSelectedInputs called with:`, {
      inputKeys: Object.keys(inputs || {}),
      selectedOutputsCount: selectedOutputs?.length || 0,
      workflowRunId
    });
    console.log(`[BaseNodeExecutor] selectedOutputs:`, JSON.stringify(selectedOutputs, null, 2));
    console.log(`[BaseNodeExecutor] inputs:`, JSON.stringify(inputs, null, 2));

    if (!selectedOutputs || selectedOutputs.length === 0) {
      console.log(`[BaseNodeExecutor] No selectedOutputs, returning original inputs`);
      return { ...inputs };
    }

    const filteredResults = {};

    // Process each selected output
    for (const selection of selectedOutputs) {
      const { nodeId, key } = selection;
      console.log(`[BaseNodeExecutor] Processing selection: nodeId=${nodeId}, key=${key}`);

      // Check if we have this node in direct inputs
      if (inputs[nodeId]) {
        console.log(`[BaseNodeExecutor] Found nodeId ${nodeId} in direct inputs`);
        // If this is the first output we're including from this node, initialize the object
        if (!filteredResults[nodeId]) {
          filteredResults[nodeId] = {};
        }

        // Extract the specific key from the node output
        this.extractValueFromNode(inputs[nodeId], key, filteredResults[nodeId]);
        console.log(`[BaseNodeExecutor] After extracting ${key} from ${nodeId}:`, filteredResults[nodeId]);
      } else {
        console.log(`[BaseNodeExecutor] NodeId ${nodeId} not found in direct inputs, fetching from database`);
        // This node isn't in direct inputs, we need to fetch it
        const missingNodeResults = await this.getSpecificNodeResults(workflowRunId, [nodeId]);
        console.log(`[BaseNodeExecutor] Fetched missing node results:`, JSON.stringify(missingNodeResults, null, 2));

        if (missingNodeResults[nodeId]) {
          console.log(`[BaseNodeExecutor] Found missing nodeId ${nodeId} in database results`);
          // If this is the first output we're including from this node, initialize the object
          if (!filteredResults[nodeId]) {
            filteredResults[nodeId] = {};
          }

          // Extract the specific key from the node output
          this.extractValueFromNode(missingNodeResults[nodeId], key, filteredResults[nodeId]);
          console.log(`[BaseNodeExecutor] After extracting ${key} from missing ${nodeId}:`, filteredResults[nodeId]);
        } else {
          console.log(`[BaseNodeExecutor] Missing nodeId ${nodeId} not found in database results`);
        }
      }
    }

    console.log(`[BaseNodeExecutor] Final filteredResults:`, JSON.stringify(filteredResults, null, 2));
    return filteredResults;
  }

  /**
   * Extract a specific value from a node output and store it in the target object
   * @param {Object} nodeOutput - The node output object
   * @param {string} key - The key to extract
   * @param {Object} target - The target object to store the value
   */
  extractValueFromNode(nodeOutput, key, target) {
    console.log(`[BaseNodeExecutor] extractValueFromNode: key=${key}, nodeOutput keys:`, Object.keys(nodeOutput || {}));
    console.log(`[BaseNodeExecutor] nodeOutput:`, JSON.stringify(nodeOutput, null, 2));

    if (key === 'result') {
      console.log(`[BaseNodeExecutor] Extracting 'result' key, value:`, nodeOutput.result);
      target.result = nodeOutput.result;
    } else if (key.includes('.')) {
      console.log(`[BaseNodeExecutor] Extracting nested key: ${key}`);
      // Handle nested paths like 'output.text'
      const parts = key.split('.');
      let value = nodeOutput;
      let valid = true;

      for (const part of parts) {
        console.log(`[BaseNodeExecutor] Checking part: ${part}, current value:`, value);
        if (value && typeof value === 'object' && part in value) {
          value = value[part];
          console.log(`[BaseNodeExecutor] Found part ${part}, new value:`, value);
        } else {
          console.log(`[BaseNodeExecutor] Part ${part} not found or invalid`);
          valid = false;
          break;
        }
      }

      if (valid) {
        // Use the last part of the key as the property name
        const propertyName = parts[parts.length - 1];
        console.log(`[BaseNodeExecutor] Setting ${propertyName} = `, value);
        target[propertyName] = value;
      } else {
        console.log(`[BaseNodeExecutor] Nested key ${key} extraction failed`);
      }
    } else if (nodeOutput[key] !== undefined) {
      console.log(`[BaseNodeExecutor] Direct property access: ${key} = `, nodeOutput[key]);
      // Direct property access
      target[key] = nodeOutput[key];
    } else {
      console.log(`[BaseNodeExecutor] Key ${key} not found in nodeOutput`);
    }
  }

  /**
   * Get results for specific nodes
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {Array} nodeIds - Array of node IDs to get results for
   * @param {boolean} simplified - Whether to return a simplified version of the results
   * @returns {Promise<Object>} - The specified node results
   */
  async getSpecificNodeResults(workflowRunId, nodeIds, simplified = false) {
    try {
      // Get all node runs for the specified nodes
      const nodeRuns = await this.prisma.nodeRun.findMany({
        where: {
          workflowRunId: workflowRunId,
          nodeId: { in: nodeIds },
          status: 'SUCCESS' // Only include successful nodes
        }
      });

      // Create a map of node IDs to their outputs
      const nodeOutputs = {};
      for (const nodeRun of nodeRuns) {
        if (nodeRun.output) {
          try {
            const output = typeof nodeRun.output === 'string'
              ? JSON.parse(nodeRun.output)
              : nodeRun.output;

            if (simplified) {
              // For simplified version, just include the result if available
              nodeOutputs[nodeRun.nodeId] = {
                result: output.result || JSON.stringify(output).substring(0, 500) + '...'
              };
            } else {
              nodeOutputs[nodeRun.nodeId] = output;
            }
          } catch (error) {
            console.error(`Error parsing output for node ${nodeRun.nodeId}:`, error);
          }
        }
      }

      return nodeOutputs;
    } catch (error) {
      console.error(`Error getting specific node results:`, error);
      return {};
    }
  }

  /**
   * Emit node completion via WebSockets
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node
   * @param {boolean} success - Whether the execution was successful
   * @param {Object} output - The output from the node
   * @param {Error|null} error - Any error that occurred
   */
  emitNodeCompletion(workflowRunId, nodeId, success, output, error) {
    this.io.emit('nodeRunProgress', {
      workflowRunId,
      nodeId,
      status: success ? 'SUCCESS' : 'FAILED',
      message: success ? `Node ${nodeId} completed successfully` : `Node ${nodeId} failed: ${error?.message}`,
      output
    });
  }
}

module.exports = BaseNodeExecutor;
