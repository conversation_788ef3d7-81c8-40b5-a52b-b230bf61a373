// Debug script to test task node contextual data processing
// This simulates what happens in the TaskNodeExecutor

const testData = {
  // Simulated inputs from previous nodes (what the task node receives)
  inputs: {
    "speech-to-text-1751881985447": {
      transcription: "This is a demo transcription generated without requiring a file upload. In a real workflow, you would need to upload a file or provide a URL.",
      source: 'Demo mode (no file required)',
      sourceType: 'audio',
      demoMode: true,
      fileCount: 0
    }
  },
  
  // Task node configuration
  taskNode: {
    id: "task-1751883453204",
    data: {
      label: "Task",
      assignee: "851a34a7-d8a0-4802-b958-8ab75aee483a",
      requiresUserInput: true, // This should be set!
      selectedOutputs: [
        {
          key: "transcription",
          label: "Speech to Text - transcription",
          nodeId: "speech-to-text-1751881985447"
        }
      ]
    }
  }
};

// Simulate the formatContextualData function from TaskNodeExecutor
function formatContextualData(processedInputs, selectedOutputs) {
  const contextualData = {};

  selectedOutputs.forEach(selection => {
    const { nodeId, key, label } = selection;

    if (processedInputs[nodeId]) {
      let value;

      if (key === 'result') {
        value = processedInputs[nodeId].result;
      } else if (key.includes('.')) {
        // Handle nested paths
        const parts = key.split('.');
        let current = processedInputs[nodeId];
        for (const part of parts) {
          if (current && typeof current === 'object' && part in current) {
            current = current[part];
          } else {
            current = undefined;
            break;
          }
        }
        value = current;
      } else {
        value = processedInputs[nodeId][key];
      }

      // Add to contextual data with the provided label or default to key
      if (value !== undefined) {
        contextualData[label || `${nodeId}.${key}`] = value;
      }
    }
  });

  return contextualData;
}

// Test the function
console.log("=== Testing Contextual Data Processing ===");
console.log("Inputs:", JSON.stringify(testData.inputs, null, 2));
console.log("Selected Outputs:", JSON.stringify(testData.taskNode.data.selectedOutputs, null, 2));

const result = formatContextualData(testData.inputs, testData.taskNode.data.selectedOutputs);
console.log("Resulting Contextual Data:", JSON.stringify(result, null, 2));

// Check if the task node has requiresUserInput
console.log("=== Task Node Configuration Check ===");
console.log("requiresUserInput:", testData.taskNode.data.requiresUserInput);
console.log("assignee:", testData.taskNode.data.assignee);

if (testData.taskNode.data.requiresUserInput && testData.taskNode.data.assignee) {
  console.log("✅ Task node is properly configured for user input");
} else {
  console.log("❌ Task node is missing requiresUserInput or assignee");
}
